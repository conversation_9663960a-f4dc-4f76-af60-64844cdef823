/* News Page - Space Theme */
.news-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding-top: 70px; /* Account for fixed header */
}

/* Hero Section */
.news-hero {
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.news-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.news-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  text-align: center;
}

.news-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.news-hero-highlight {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.news-hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #b3b3b3;
  max-width: 600px;
  margin: 0 auto;
}

/* Content Section */
.news-content {
  padding: 2rem 0 4rem;
  position: relative;
}

.news-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.05), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.03), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.news-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Controls */
.news-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.news-search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.news-search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem 0.75rem 3rem;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.news-search-input:focus {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.news-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.news-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.5);
}

.news-category-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.news-category-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.news-category-btn:hover,
.news-category-btn.active {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: white;
}

/* Featured Article */
.news-featured {
  margin-bottom: 3rem;
}

.news-featured-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  transition: all 0.3s ease;
}

.news-featured-content:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.news-featured-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.news-featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-featured-content:hover .news-featured-image img {
  transform: scale(1.05);
}

.news-featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.news-featured-info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-featured-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.news-category-tag {
  background: rgba(79, 70, 229, 0.2);
  color: #a5b4fc;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.news-read-time {
  color: #b3b3b3;
  font-size: 0.8rem;
}

.news-featured-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  color: white;
  line-height: 1.3;
}

.news-featured-excerpt {
  font-size: 1rem;
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.news-featured-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.news-author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.news-author-name {
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
}

.news-publish-date {
  color: #b3b3b3;
  font-size: 0.8rem;
}

.news-featured-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.news-tag {
  background: rgba(255, 255, 255, 0.1);
  color: #d1d5db;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.75rem;
}

/* Articles Grid */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.news-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.news-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.news-card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.news-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-card-image img {
  transform: scale(1.05);
}

.news-card-category {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.news-card-content {
  padding: 1.5rem;
}

.news-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #b3b3b3;
}

.news-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: white;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card-excerpt {
  font-size: 0.9rem;
  color: #d1d5db;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.news-card-author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.news-card-author-name {
  font-size: 0.85rem;
  color: #d1d5db;
  font-weight: 500;
}

.news-card-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.news-card-tag {
  background: rgba(255, 255, 255, 0.1);
  color: #b3b3b3;
  padding: 0.2rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
}

/* No Results */
.news-no-results {
  text-align: center;
  padding: 3rem;
  color: #b3b3b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .news-hero-title {
    font-size: 2.5rem;
  }

  .news-hero-subtitle {
    font-size: 1.1rem;
  }

  .news-container {
    padding: 0 1rem;
  }

  .news-featured-content {
    grid-template-columns: 1fr;
  }

  .news-featured-image {
    height: 250px;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .news-card {
    max-width: 100%;
    margin: 0 auto;
  }

  .news-card-image {
    height: 220px;
  }

  .news-category-filters {
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .news-category-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .news-hero {
    padding: 2rem 0;
  }

  .news-hero-title {
    font-size: 2rem;
  }

  .news-container {
    padding: 0 0.75rem;
  }

  .news-featured-info {
    padding: 1.5rem;
  }

  .news-card-content {
    padding: 1rem;
  }

  .news-card-image {
    height: 200px;
  }

  .news-grid {
    gap: 1.25rem;
  }

  .news-search-container {
    max-width: 100%;
  }

  .news-category-filters {
    gap: 0.4rem;
  }

  .news-category-btn {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }
}
