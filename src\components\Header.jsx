import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import './Header.css';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout, isAuthenticated } = useAuth();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const handleLogout = () => {
    logout();
    setShowUserDropdown(false);
    navigate('/');
  };

  const toggleUserDropdown = () => {
    setShowUserDropdown(!showUserDropdown);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showUserDropdown && !event.target.closest('.user-menu')) {
        setShowUserDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserDropdown]);

  const navigationItems = [
    { name: 'Home', href: '/' },
    { name: 'News', href: '/news' },
    { name: 'Technology', href: '/technology' },
    { name: 'Our Planet', href: '/our-planet' },
    { name: 'Health & Science', href: '/health-science' }
  ];

  return (
    <header className={`header ${isScrolled ? 'scrolled' : ''}`}>
      <div className="header-container">
        {/* Logo/Brand */}
        <div className="header-logo">
          <Link to="/" className="logo-link">
            <span className="logo-text">MB Space</span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="header-nav desktop-nav">
          <ul className="nav-list">
            {navigationItems.map((item, index) => (
              <li key={index} className="nav-item">
                <Link
                  to={item.href}
                  className={`nav-link ${location.pathname === item.href ? 'active' : ''}`}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* Auth Actions */}
        <div className="header-actions">
          {isAuthenticated ? (
            <div className="user-menu">
              <button className="user-profile-btn" onClick={toggleUserDropdown}>
                <img src={user.avatar} alt={user.displayName} className="user-avatar" />
                <span className="user-name">{user.displayName}</span>
                <svg className="dropdown-arrow" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7 10l5 5 5-5z"/>
                </svg>
              </button>

              {showUserDropdown && (
                <div className="user-dropdown">
                  <div className="user-dropdown-header">
                    <img src={user.avatar} alt={user.displayName} className="dropdown-avatar" />
                    <div className="dropdown-user-info">
                      <span className="dropdown-name">{user.displayName}</span>
                      <span className="dropdown-email">{user.email}</span>
                    </div>
                  </div>
                  <div className="user-dropdown-menu">
                    <Link to="/profile" className="dropdown-item" onClick={() => setShowUserDropdown(false)}>
                      <svg viewBox="0 0 24 24" fill="currentColor" className="dropdown-icon">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                      Profile
                    </Link>
                    
                    <button className="dropdown-item-logout" onClick={handleLogout}>
                      <svg viewBox="0 0 24 24" fill="currentColor" className="dropdown-icon">
                        <path d="M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 012 2v2h-2V4H4v16h10v-2h2v2a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2h10z"/>
                      </svg>
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="auth-buttons">
              <Link to="/login" className="login-btn">
                Sign In
              </Link>
              <Link to="/signup" className="signup-btn">
                Sign Up
              </Link>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className={`mobile-menu-btn ${isMenuOpen ? 'active' : ''}`}
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
          aria-expanded={isMenuOpen}
        >
          <span className="hamburger-line"></span>
          <span className="hamburger-line"></span>
          <span className="hamburger-line"></span>
        </button>
      </div>

      {/* Mobile Navigation */}
      <nav className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>
        <div className="mobile-nav-content">
          <ul className="mobile-nav-list">
            {navigationItems.map((item, index) => (
              <li key={index} className="mobile-nav-item">
                <Link
                  to={item.href}
                  className={`mobile-nav-link ${location.pathname === item.href ? 'active' : ''}`}
                  onClick={closeMenu}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
          <div className="mobile-actions">
            {isAuthenticated ? (
              <div className="mobile-user-section">
                <div className="mobile-user-info">
                  <img src={user.avatar} alt={user.displayName} className="mobile-user-avatar" />
                  <div className="mobile-user-details">
                    <span className="mobile-user-name">{user.displayName}</span>
                    <span className="mobile-user-email">{user.email}</span>
                  </div>
                </div>
                <div className="mobile-user-actions">
                  <Link to="/profile" className="mobile-profile-btn" onClick={closeMenu}>
                    Profile
                  </Link>
                  <button className="mobile-logout-btn" onClick={() => { handleLogout(); closeMenu(); }}>
                    Sign Out
                  </button>
                </div>
              </div>
            ) : (
              <div className="mobile-auth-buttons">
                <Link to="/login" className="mobile-login-btn" onClick={closeMenu}>
                  Sign In
                </Link>
                <Link to="/signup" className="mobile-signup-btn" onClick={closeMenu}>
                  Sign Up
                </Link>
              </div>
            )}
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div
          className="mobile-overlay"
          onClick={closeMenu}
          aria-hidden="true"
        ></div>
      )}
    </header>
  );
};

export default Header;
