/* Authentication Pages - Enhanced Space Theme */
.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 70px 0 2rem;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 25px 35px, rgba(79, 70, 229, 0.3), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(124, 58, 237, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(236, 72, 153, 0.2), transparent);
  background-repeat: repeat;
  background-size: 200px 150px;
  pointer-events: none;
  animation: twinkle 4s ease-in-out infinite alternate;
}

@keyframes twinkle {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

.auth-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.auth-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.auth-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  padding: 3rem;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), rgba(124, 58, 237, 0.5), transparent);
}

.auth-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(79, 70, 229, 0.03), transparent, rgba(124, 58, 237, 0.03), transparent);
  animation: rotate 20s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced Header */
.auth-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 2;
}

.auth-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(124, 58, 237, 0.2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(79, 70, 229, 0.3);
  box-shadow:
    0 0 30px rgba(79, 70, 229, 0.3),
    0 0 0 8px rgba(79, 70, 229, 0.1),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
  animation: iconFloat 3s ease-in-out infinite alternate;
}

@keyframes iconFloat {
  from {
    transform: translateY(0px);
    box-shadow:
      0 0 30px rgba(79, 70, 229, 0.3),
      0 0 0 8px rgba(79, 70, 229, 0.1),
      inset 0 2px 4px rgba(255, 255, 255, 0.1);
  }
  to {
    transform: translateY(-5px);
    box-shadow:
      0 5px 35px rgba(79, 70, 229, 0.4),
      0 0 0 8px rgba(79, 70, 229, 0.15),
      inset 0 2px 4px rgba(255, 255, 255, 0.15);
  }
}

.space-icon {
  width: 40px;
  height: 40px;
  color: #a5b4fc;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.auth-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-highlight {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  font-size: 1rem;
  line-height: 1.6;
  color: #b3b3b3;
  margin: 0;
}

/* Form */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
  animation: formGroupSlideIn 0.4s ease-out;
}

@keyframes formGroupSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }
.form-group:nth-child(6) { animation-delay: 0.6s; }

.form-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #d1d5db;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.form-label::before {
  content: '';
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(79, 70, 229, 0.5);
}

.form-input {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 1rem 1.25rem;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  position: relative;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-input:focus {
  border-color: rgba(79, 70, 229, 0.6);
  box-shadow:
    0 0 0 3px rgba(79, 70, 229, 0.15),
    0 8px 25px rgba(79, 70, 229, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  transition: color 0.3s ease;
}

.form-input:focus::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-input.error {
  border-color: rgba(239, 68, 68, 0.6);
  box-shadow:
    0 0 0 3px rgba(239, 68, 68, 0.15),
    0 8px 25px rgba(239, 68, 68, 0.1);
  background: rgba(239, 68, 68, 0.05);
}

.form-error {
  color: #fca5a5;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Password Input */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 1.1rem;
  padding: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.password-toggle:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(79, 70, 229, 0.2);
  border-color: rgba(79, 70, 229, 0.3);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.2);
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

/* Enhanced Password Strength */
.password-strength {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.strength-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.strength-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.strength-fill {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 3px;
  position: relative;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.strength-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 3px 3px 0 0;
}

.strength-text {
  font-size: 0.85rem;
  font-weight: 600;
  min-width: 90px;
  text-align: right;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Enhanced Avatar Selection */
.avatar-selection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.default-avatars {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.avatar-option {
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.15);
  background: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.avatar-option::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.3), transparent, rgba(124, 58, 237, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.avatar-option img {
  width: 100%;
  height: 100%;
  object-fit:fill;
  transition: transform 0.3s ease;
  
}

.avatar-option:hover {
  border-color: rgba(79, 70, 229, 0.6);
  box-shadow:
    0 0 0 4px rgba(79, 70, 229, 0.2),
    0 8px 25px rgba(79, 70, 229, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: scale(1.1) translateY(-2px);
}

.avatar-option:hover::before {
  opacity: 1;
}

.avatar-option:hover img {
  transform: scale(1.05);
}

.avatar-option.selected {
  border-color: rgba(79, 70, 229, 0.8);
  box-shadow:
    0 0 0 4px rgba(79, 70, 229, 0.3),
    0 12px 30px rgba(79, 70, 229, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: scale(1.15) translateY(-4px);
}

.avatar-option.selected::before {
  opacity: 1;
  background: linear-gradient(45deg, rgba(79, 70, 229, 0.4), rgba(124, 58, 237, 0.4), rgba(236, 72, 153, 0.4));
}

.avatar-option.selected img {
  transform: scale(1.1);
}

.custom-avatar-upload {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  justify-content: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

.avatar-input {
  display: none;
}

.avatar-upload-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));
  border: 1px solid rgba(79, 70, 229, 0.3);
  color: #a5b4fc;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.avatar-upload-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.avatar-upload-btn:hover::before {
  left: 100%;
}

.avatar-upload-btn:hover {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.25), rgba(124, 58, 237, 0.25));
  border-color: rgba(79, 70, 229, 0.5);
  color: white;
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(79, 70, 229, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.upload-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.avatar-upload-btn:hover .upload-icon {
  transform: scale(1.1);
}

.custom-avatar-preview {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(79, 70, 229, 0.8);
  box-shadow:
    0 0 0 4px rgba(79, 70, 229, 0.2),
    0 8px 25px rgba(79, 70, 229, 0.15);
  position: relative;
  animation: avatarGlow 2s ease-in-out infinite alternate;
}

@keyframes avatarGlow {
  from {
    box-shadow:
      0 0 0 4px rgba(79, 70, 229, 0.2),
      0 8px 25px rgba(79, 70, 229, 0.15);
  }
  to {
    box-shadow:
      0 0 0 4px rgba(79, 70, 229, 0.4),
      0 8px 25px rgba(79, 70, 229, 0.25);
  }
}

.custom-avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #d1d5db;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: rgba(79, 70, 229, 0.8);
  border-color: rgba(79, 70, 229, 0.8);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-password-btn {
  background: none;
  border: none;
  color: #a5b4fc;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.forgot-password-btn:hover {
  color: white;
}

/* Enhanced Submit Button */
.auth-submit-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  padding: 1rem 2rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 25px rgba(79, 70, 229, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.auth-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.auth-submit-btn:hover:not(:disabled)::before {
  left: 100%;
}

.auth-submit-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 50%, #db2777 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(79, 70, 229, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.auth-submit-btn:active:not(:disabled) {
  transform: translateY(-1px) scale(1.01);
  box-shadow:
    0 8px 20px rgba(79, 70, 229, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.auth-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.auth-footer {
  text-align: center;
  margin-top: 1rem;
}

.auth-footer p {
  color: #b3b3b3;
  font-size: 0.9rem;
  margin: 0;
}

.auth-link {
  color: #a5b4fc;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
}

.auth-link:hover {
  color: white;
}

/* Error and Success Banners */
.auth-error-banner,
.auth-success-banner {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.auth-error-banner {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

.auth-success-banner {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #6ee7b7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 0 1rem;
  }

  .auth-card {
    padding: 2rem 1.5rem;
  }

  .auth-title {
    font-size: 2rem;
  }

  .auth-subtitle {
    font-size: 0.9rem;
  }

  .default-avatars {
    gap: 0.5rem;
  }

  .avatar-option {
    width: 50px;
    height: 50px;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 1.5rem 1rem;
  }

  .auth-title {
    font-size: 1.8rem;
  }

  .custom-avatar-upload {
    flex-direction: column;
    gap: 0.5rem;
  }

  .avatar-upload-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
