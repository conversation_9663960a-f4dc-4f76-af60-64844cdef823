import React from 'react';
import './PlanetCondition.css';
import { useNavigate } from 'react-router-dom'


const PlanetCondition = () => {
  const navigate = useNavigate();
  const planetStories = [
    {
      id: 1,
      title: "New Research Ties Industrial Pollution in Joppa to Higher Rates of Asthma, Respiratory Issues",
      description: "A recent study by Texas A&M University has confirmed what many residents of Joppa—a historically Black neighborhood in Dallas—have long suspected: industrial pollution is harming their health. Researchers found that nearly 1 in 5 residents reported having asthma, more than double the regional average. About 35% were at risk for chronic obstructive pulmonary disease (COPD), based on clinical screening",
      image: "https://images.unsplash.com/photo-1611273426858-450d8e3c9fce?w=500&h=300&fit=crop&crop=center",
      readMore: "Read more",
      isLarge: true
    },
    {
      id: 2,
      title: "Wildlife Worldwide Contaminated by Flame Retardants: New Map",
      description: "Wild animals across every continent are contaminated with flame retardant chemicals, according to a new...",
      image: "https://www.ehn.org/media-library/killer-whales.jpg?id=49833019&width=1200&height=600&coordinates=0%2C275%2C0%2C40",
      readMore: "Read more"
    },
    {
      id: 3,
      title: "Switzerland Sets Its Sights on Combatting Greenwashing in Fina...",
      description: "Switzerland's Federal Department of Finance (FDF) has declared its intention to put forward regulation...",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    },
    {
      id: 4,
      title: "For years, Japan Tried to Keep Their Existence a Secret, But the...",
      description: "As a young boy in school, Masaki Sashima would be dragged out of his classroom and beaten by...",
      image: "https://images.unsplash.com/photo-1490806843957-31f4c9a91c65?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    },
    {
      id: 5,
      title: "Prospect of $4 Million a Year for Trash Collection Leaves New Ken...",
      description: "The prospect of paying nearly $4 million a year for curbside trash collection — more than four times what...",
      image: "https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    }
  ];

  const handleViewAll = () => {
    navigate('/our-planet');
  };

  return (
    <section className="planet-condition">
      <div className="planet-container">
        <div className="planet-header">
          <h2 className="planet-title">Current Condition of Our Planet</h2>
          <button className="planet-view-all" onClick={handleViewAll}>
            see all <span className="arrow" >→</span>
          </button>
        </div>

        <div className="planet-grid">
          {planetStories.map((story) => (
            <article key={story.id} className={`planet-story ${story.isLarge ? 'large-story' : ''}`}>
              <div className="story-image">
                <img src={story.image} alt={story.title} />
              </div>
              <div className="story-content">
                <h3 className="story-title">{story.title}</h3>
                <p className="story-description">
                  {story.description}
                </p>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PlanetCondition;
