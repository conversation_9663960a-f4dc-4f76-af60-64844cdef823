import './Hero.css';

const Hero = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 70; // Account for fixed header
      const elementPosition = element.offsetTop - headerHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="hero-title">
            Explore the
            <span className="hero-title-highlight"> Universe</span>
          </h1>
          <p className="hero-subtitle">
            Discover the latest space exploration news, cutting-edge technology,
            and fascinating insights about our cosmos. Join us on an incredible
            journey through the stars.
          </p>
          <div className="hero-actions">
            <button
              className="hero-btn hero-btn-primary"
              onClick={() => scrollToSection('latest-stories')}
              aria-label="Scroll to Latest Stories section"
            >
              Start Exploring
            </button>
            <button
              className="hero-btn hero-btn-secondary"
              onClick={() => scrollToSection('planet-condition')}
              aria-label="Scroll to Planet Condition section"
            >
              Learn More
            </button>
          </div>
        </div>
        <div className="hero-visual">
          <div className="hero-stars"></div>
          <div className="hero-planet"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
