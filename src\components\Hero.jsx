import React from 'react';
import { scrollToSection } from '../utils/navigation';
import './Hero.css';

const Hero = () => {

  return (
    <section className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="hero-title">
            Explore the
            <span className="hero-title-highlight"> Universe</span>
          </h1>
          <p className="hero-subtitle">
            Discover the latest space exploration news, cutting-edge technology,
            and fascinating insights about our cosmos. Join us on an incredible
            journey through the stars.
          </p>
          <div className="hero-actions">
            <button
              className="hero-btn hero-btn-primary"
              onClick={() => scrollToSection('latest-stories')}
              aria-label="Scroll to Latest Stories section"
            >
              Start Exploring
            </button>
            <button
              className="hero-btn hero-btn-secondary"
              onClick={() => scrollToSection('planet-condition')}
              aria-label="Scroll to Planet Condition section"
            >
              Learn More
            </button>
          </div>

          {/* Additional Navigation Links */}
          <div className="hero-navigation">
            <div className="hero-nav-title">Quick Navigation</div>
            <div className="hero-nav-links">
              <button
                className="hero-nav-btn"
                onClick={() => scrollToSection('trending-news')}
                aria-label="Scroll to Trending News section"
              >
                <svg viewBox="0 0 24 24" fill="currentColor" className="nav-icon">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
                Trending News
              </button>
              <button
                className="hero-nav-btn"
                onClick={() => scrollToSection('videos')}
                aria-label="Scroll to Videos section"
              >
                <svg viewBox="0 0 24 24" fill="currentColor" className="nav-icon">
                  <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
                </svg>
                Videos
              </button>
              <button
                className="hero-nav-btn"
                onClick={() => scrollToSection('podcasts')}
                aria-label="Scroll to Podcasts section"
              >
                <svg viewBox="0 0 24 24" fill="currentColor" className="nav-icon">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                Podcasts
              </button>
            </div>
          </div>
        </div>
        <div className="hero-visual">
          <div className="hero-stars"></div>
          <div className="hero-planet"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
