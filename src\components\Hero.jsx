import './Hero.css';
import { useNavigate } from 'react-router-dom';


const Hero = () => {

  const navigate = useNavigate();

  return (
    <section
      className="hero"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)),)`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="hero-title">
            Explore the
            <span className="hero-title-highlight"> Universe</span>
          </h1>
          <p className="hero-subtitle">
            Discover the latest space exploration news, cutting-edge technology,
            and fascinating insights about our cosmos. Join us on an incredible
            journey through the stars.
          </p>
          <div className="hero-actions">
            <button className="hero-btn hero-btn-primary" onClick={() => navigate('/news')}>
              Start Exploring
            </button>
          </div>
        </div>
        <div className="hero-visual">
          <div className="hero-stars"></div>
          <div className="hero-planet"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
