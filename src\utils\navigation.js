/**
 * Navigation utility functions for smooth scrolling and cross-page navigation
 */

/**
 * Scrolls to a specific section on the current page
 * @param {string} sectionId - The ID of the section to scroll to
 * @param {number} headerOffset - Offset to account for fixed header (default: 70px)
 */
export const scrollToSection = (sectionId, headerOffset = 70) => {
  const element = document.getElementById(sectionId);
  if (element) {
    const elementPosition = element.offsetTop - headerOffset;
    
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  }
};

/**
 * Navigates to home page and scrolls to a specific section
 * @param {string} sectionId - The ID of the section to scroll to
 * @param {function} navigate - React Router navigate function
 * @param {string} currentPath - Current pathname from useLocation
 * @param {number} headerOffset - Offset to account for fixed header (default: 70px)
 */
export const navigateToSection = (sectionId, navigate, currentPath, headerOffset = 70) => {
  if (currentPath !== '/') {
    // Navigate to home page first
    navigate('/');
    // Wait for navigation to complete, then scroll
    setTimeout(() => {
      scrollToSection(sectionId, headerOffset);
    }, 100);
  } else {
    // Already on home page, just scroll
    scrollToSection(sectionId, headerOffset);
  }
};

/**
 * Checks if smooth scroll is supported and provides fallback
 * @param {string} sectionId - The ID of the section to scroll to
 * @param {number} headerOffset - Offset to account for fixed header (default: 70px)
 */
export const smoothScrollWithFallback = (sectionId, headerOffset = 70) => {
  const element = document.getElementById(sectionId);
  if (!element) return;

  const elementPosition = element.offsetTop - headerOffset;

  // Check if smooth scroll is supported
  if ('scrollBehavior' in document.documentElement.style) {
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  } else {
    // Fallback for browsers that don't support smooth scroll
    const startPosition = window.pageYOffset;
    const distance = elementPosition - startPosition;
    const duration = 800; // 800ms animation
    let start = null;

    const step = (timestamp) => {
      if (!start) start = timestamp;
      const progress = timestamp - start;
      const percentage = Math.min(progress / duration, 1);
      
      // Easing function for smooth animation
      const easeInOutCubic = percentage < 0.5 
        ? 4 * percentage * percentage * percentage 
        : (percentage - 1) * (2 * percentage - 2) * (2 * percentage - 2) + 1;
      
      window.scrollTo(0, startPosition + distance * easeInOutCubic);
      
      if (progress < duration) {
        window.requestAnimationFrame(step);
      }
    };
    
    window.requestAnimationFrame(step);
  }
};

/**
 * Validates if a section exists on the page
 * @param {string} sectionId - The ID of the section to check
 * @returns {boolean} - True if section exists, false otherwise
 */
export const sectionExists = (sectionId) => {
  return document.getElementById(sectionId) !== null;
};

/**
 * Gets the current scroll position relative to a section
 * @param {string} sectionId - The ID of the section
 * @returns {object} - Object with section position info
 */
export const getSectionPosition = (sectionId) => {
  const element = document.getElementById(sectionId);
  if (!element) return null;

  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  
  return {
    top: rect.top + scrollTop,
    bottom: rect.bottom + scrollTop,
    height: rect.height,
    isVisible: rect.top >= 0 && rect.bottom <= window.innerHeight,
    isPartiallyVisible: rect.top < window.innerHeight && rect.bottom > 0
  };
};

/**
 * Highlights the active navigation item based on current scroll position
 * @param {Array} sections - Array of section IDs to check
 * @param {function} setActiveSection - Function to set the active section
 * @param {number} headerOffset - Offset to account for fixed header (default: 70px)
 */
export const updateActiveSection = (sections, setActiveSection, headerOffset = 70) => {
  const scrollPosition = window.scrollY + headerOffset + 100; // Add some buffer
  
  for (let i = sections.length - 1; i >= 0; i--) {
    const section = document.getElementById(sections[i]);
    if (section && section.offsetTop <= scrollPosition) {
      setActiveSection(sections[i]);
      break;
    }
  }
};
