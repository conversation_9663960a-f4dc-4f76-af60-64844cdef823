import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Default space-themed avatars
  const defaultAvatars = [
    "https://cdn-icons-png.flaticon.com/512/9256/9256463.png",
    "https://cdn-icons-png.flaticon.com/512/3419/3419410.png",
    "https://cdn-icons-png.flaticon.com/512/547/547413.png",
    "https://cdn-icons-png.flaticon.com/512/547/547420.png",
    "https://cdn-icons-png.flaticon.com/512/3049/3049617.png"
  ];

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem('spaceUser');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('spaceUser');
      }
    }
    setLoading(false);
  }, []);

  // Save user to localStorage whenever user state changes
  useEffect(() => {
    if (user) {
      localStorage.setItem('spaceUser', JSON.stringify(user));
    } else {
      localStorage.removeItem('spaceUser');
    }
  }, [user]);

  const signup = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Check if user already exists (simulate)
      const existingUsers = JSON.parse(localStorage.getItem('spaceUsers') || '[]');
      const userExists = existingUsers.find(u => u.email === userData.email);
      
      if (userExists) {
        throw new Error('User with this email already exists');
      }

      // Create new user
      const newUser = {
        id: Date.now().toString(),
        email: userData.email,
        displayName: userData.displayName,
        avatar: userData.avatar || defaultAvatars[Math.floor(Math.random() * defaultAvatars.length)],
        createdAt: new Date().toISOString(),
        preferences: {
          newsletter: true,
          notifications: true,
          theme: 'space'
        }
      };

      // Save to "database" (localStorage)
      existingUsers.push({ ...newUser, password: userData.password });
      localStorage.setItem('spaceUsers', JSON.stringify(existingUsers));

      // Set current user (without password)
      const {  ...userWithoutPassword } = newUser;
      setUser(userWithoutPassword);
      
      return { success: true, user: userWithoutPassword };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check credentials
      const existingUsers = JSON.parse(localStorage.getItem('spaceUsers') || '[]');
      const user = existingUsers.find(u => 
        u.email === credentials.email && u.password === credentials.password
      );

      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Set current user (without password)
      const {  ...userWithoutPassword } = user;
      setUser(userWithoutPassword);

      return { success: true, user: userWithoutPassword };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setError(null);
  };

  const updateProfile = async (updates) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedUser = { ...user, ...updates };
      
      // Update in "database"
      const existingUsers = JSON.parse(localStorage.getItem('spaceUsers') || '[]');
      const userIndex = existingUsers.findIndex(u => u.id === user.id);
      if (userIndex !== -1) {
        existingUsers[userIndex] = { ...existingUsers[userIndex], ...updates };
        localStorage.setItem('spaceUsers', JSON.stringify(existingUsers));
      }

      setUser(updatedUser);
      return { success: true, user: updatedUser };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email) => {
    setLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Check if user exists
      const existingUsers = JSON.parse(localStorage.getItem('spaceUsers') || '[]');
      const user = existingUsers.find(u => u.email === email);

      if (!user) {
        throw new Error('No account found with this email address');
      }

      // Simulate sending reset email
      return { success: true, message: 'Password reset email sent successfully' };
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    loading,
    error,
    signup,
    login,
    logout,
    updateProfile,
    resetPassword,
    defaultAvatars,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
