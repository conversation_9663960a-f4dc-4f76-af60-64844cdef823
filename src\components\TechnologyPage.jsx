import React, { useState, useEffect } from 'react';
import { useNASA } from '../contexts/NASAContext';
import './TechnologyPage.css';

// Initial technologies data (moved outside component)
const initialTechnologies = [
    {
      id: 1,
      title: "Nuclear Thermal Propulsion Systems",
      description: "Revolutionary propulsion technology that could cut Mars travel time in half using nuclear thermal engines.",
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&h=400&fit=crop&crop=center",
      category: "Propulsion",
      status: "In Development",
      organization: "NASA",
      timeline: "2030-2035",
      featured: true,
      specs: {
        "Thrust": "25,000 lbf",
        "Specific Impulse": "900 seconds",
        "Fuel Type": "Liquid Hydrogen"
      },
      benefits: ["50% faster Mars transit", "Higher payload capacity", "Reduced mission costs"]
    },
    {
      id: 2,
      title: "Autonomous Mars Rovers with AI",
      description: "Next-generation rovers equipped with advanced AI for independent exploration and scientific analysis.",
      image: "https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=800&h=400&fit=crop&crop=center",
      category: "Robotics",
      status: "Testing",
      organization: "JPL",
      timeline: "2026-2028",
      featured: false,
      specs: {
        "Processing Power": "100x current rovers",
        "Autonomy Level": "Level 4",
        "Operating Range": "50 km radius"
      },
      benefits: ["Real-time decision making", "Extended exploration range", "Reduced Earth dependency"]
    },
    {
      id: 3,
      title: "Quantum Communication Networks",
      description: "Secure, instantaneous communication across vast distances using quantum entanglement principles.",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop&crop=center",
      category: "Communications",
      status: "Research",
      organization: "ESA",
      timeline: "2035-2040",
      featured: false,
      specs: {
        "Range": "Unlimited",
        "Security": "Quantum encrypted",
        "Latency": "Instantaneous"
      },
      benefits: ["Unhackable communications", "Real-time deep space contact", "Revolutionary data transfer"]
    },
    {
      id: 4,
      title: "Closed-Loop Life Support Systems",
      description: "Advanced life support technology that recycles 99% of air and water for long-duration missions.",
      image: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=800&h=400&fit=crop&crop=center",
      category: "Life Support",
      status: "Operational",
      organization: "ISS Program",
      timeline: "2024-Present",
      featured: false,
      specs: {
        "Recycling Efficiency": "99%",
        "Crew Capacity": "7 astronauts",
        "Mission Duration": "3+ years"
      },
      benefits: ["Reduced resupply needs", "Extended mission capability", "Cost savings"]
    },
    {
      id: 5,
      title: "Precision Landing Technology",
      description: "Advanced guidance systems enabling pinpoint landings on planetary surfaces with meter-level accuracy.",
      image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=800&h=400&fit=crop&crop=center",
      category: "Navigation",
      status: "Deployed",
      organization: "SpaceX",
      timeline: "2020-Present",
      featured: false,
      specs: {
        "Accuracy": "±1 meter",
        "Landing Mass": "100+ tons",
        "Success Rate": "95%"
      },
      benefits: ["Precise mission targeting", "Reusable vehicles", "Enhanced safety"]
    },
    {
      id: 6,
      title: "Solar Electric Propulsion",
      description: "Highly efficient ion propulsion systems powered by advanced solar arrays for deep space missions.",
      image: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=800&h=400&fit=crop&crop=center",
      category: "Propulsion",
      status: "Operational",
      organization: "NASA/ESA",
      timeline: "2018-Present",
      featured: false,
      specs: {
        "Specific Impulse": "3000+ seconds",
        "Power Output": "50 kW",
        "Thrust": "0.5 N"
      },
      benefits: ["Fuel efficient", "Long-duration missions", "Deep space capability"]
    }
  ];

const TechnologyPage = () => {
  const { processContentItems } = useNASA();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [technologies, setTechnologies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const techCategories = ['All', 'Propulsion', 'Robotics', 'Communications', 'Life Support', 'Navigation'];

  // Load NASA images when component mounts
  useEffect(() => {
    const loadNASAImages = async () => {
      try {
        setLoading(true);
        setError(null);

        // Process technologies to get NASA images
        const processedTechnologies = await processContentItems(
          initialTechnologies,
          { title: 'title', category: 'category' },
          'technology-page'
        );
        setTechnologies(processedTechnologies);
      } catch (err) {
        console.error('Error loading NASA images for technologies:', err);
        setError('Failed to load NASA images');
        setTechnologies(initialTechnologies);
      } finally {
        setLoading(false);
      }
    };

    loadNASAImages();
  }, [processContentItems]);

  const filteredTechnologies = technologies.filter(tech => {
    const matchesCategory = selectedCategory === 'All' || tech.category === selectedCategory;
    const matchesSearch = tech.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tech.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tech.organization.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredTech = filteredTechnologies.find(tech => tech.featured);
  const regularTechs = filteredTechnologies.filter(tech => !tech.featured);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Operational': return '#10b981';
      case 'Testing': return '#f59e0b';
      case 'In Development': return '#3b82f6';
      case 'Research': return '#8b5cf6';
      case 'Deployed': return '#059669';
      default: return '#6b7280';
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="technology-page">
        <section className="tech-hero">
          <div className="tech-hero-container">
            <div className="tech-hero-content">
              <h1 className="tech-hero-title">
                Space <span className="tech-hero-highlight">Technology</span>
              </h1>
              <p className="tech-hero-subtitle">Loading technologies...</p>
            </div>
          </div>
        </section>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="technology-page">
        <section className="tech-hero">
          <div className="tech-hero-container">
            <div className="tech-hero-content">
              <h1 className="tech-hero-title">
                Space <span className="tech-hero-highlight">Technology</span>
              </h1>
              <p className="tech-hero-subtitle">Error: {error}</p>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="technology-page">
      {/* Hero Section */}
      <section className="tech-hero">
        <div className="tech-hero-container">
          <div className="tech-hero-content">
            <h1 className="tech-hero-title">
              Space <span className="tech-hero-highlight">Technology</span>
            </h1>
            <p className="tech-hero-subtitle">
              Explore cutting-edge innovations and breakthrough technologies that are
              revolutionizing space exploration and pushing the boundaries of human capability.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="tech-content">
        <div className="tech-container">
          {/* Search and Filter Controls */}
          <div className="tech-controls">
            <div className="tech-search-container">
              <input
                type="text"
                placeholder="Search technologies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="tech-search-input"
              />
              <svg className="tech-search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>

            <div className="tech-category-filters">
              {techCategories.map(category => (
                <button
                  key={category}
                  className={`tech-category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Featured Technology */}
          {featuredTech && (
            <div className="tech-featured">
              <div className="tech-featured-content">
                <div className="tech-featured-image">
                  <img src={featuredTech.image} alt={featuredTech.title} />
                  <div className="tech-featured-badge">Featured Technology</div>
                  <div className="tech-status-badge" style={{ backgroundColor: getStatusColor(featuredTech.status) }}>
                    {featuredTech.status}
                  </div>
                </div>
                <div className="tech-featured-info">
                  <div className="tech-featured-meta">
                    <span className="tech-category-tag">{featuredTech.category}</span>
                    <span className="tech-organization">{featuredTech.organization}</span>
                    <span className="tech-timeline">{featuredTech.timeline}</span>
                  </div>
                  <h2 className="tech-featured-title">{featuredTech.title}</h2>
                  <p className="tech-featured-description">{featuredTech.description}</p>
                  
                  <div className="tech-specs">
                    <h4>Technical Specifications</h4>
                    <div className="tech-specs-grid">
                      {Object.entries(featuredTech.specs).map(([key, value]) => (
                        <div key={key} className="tech-spec-item">
                          <span className="tech-spec-label">{key}:</span>
                          <span className="tech-spec-value">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="tech-benefits">
                    <h4>Key Benefits</h4>
                    <ul className="tech-benefits-list">
                      {featuredTech.benefits.map((benefit, index) => (
                        <li key={index} className="tech-benefit-item">{benefit}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Technologies Grid */}
          <div className="tech-grid">
            {regularTechs.map(tech => (
              <article key={tech.id} className="tech-card">
                <div className="tech-card-image">
                  <img src={tech.image} alt={tech.title} />
                  <div className="tech-card-category">{tech.category}</div>
                  <div className="tech-card-status" style={{ backgroundColor: getStatusColor(tech.status) }}>
                    {tech.status}
                  </div>
                </div>
                <div className="tech-card-content">
                  <div className="tech-card-meta">
                    <span className="tech-card-org">{tech.organization}</span>
                    <span className="tech-card-timeline">{tech.timeline}</span>
                  </div>
                  <h3 className="tech-card-title">{tech.title}</h3>
                  <p className="tech-card-description">{tech.description}</p>
                  
                  <div className="tech-card-specs">
                    <h5>Key Specs</h5>
                    <div className="tech-card-specs-list">
                      {Object.entries(tech.specs).slice(0, 2).map(([key, value]) => (
                        <div key={key} className="tech-card-spec">
                          <span className="tech-card-spec-label">{key}:</span>
                          <span className="tech-card-spec-value">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="tech-card-benefits">
                    <div className="tech-card-benefits-preview">
                      {tech.benefits.slice(0, 2).map((benefit, index) => (
                        <span key={index} className="tech-benefit-tag">{benefit}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {filteredTechnologies.length === 0 && (
            <div className="tech-no-results">
              <p>No technologies found matching your search criteria.</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default TechnologyPage;
