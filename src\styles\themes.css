/* CSS Custom Properties for Theme Management */

:root {
  /* Space Theme Colors - Default Dark */
  --primary-bg: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  --secondary-bg: rgba(255, 255, 255, 0.05);
  --card-bg: rgba(255, 255, 255, 0.08);
  --border-color: rgba(255, 255, 255, 0.15);
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: rgba(255, 255, 255, 0.6);
  
  /* Accent Colors - Consistent across themes */
  --accent-primary: #4f46e5;
  --accent-secondary: #7c3aed;
  --accent-tertiary: #ec4899;
  --accent-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  
  /* Interactive States */
  --hover-bg: rgba(255, 255, 255, 0.1);
  --active-bg: rgba(79, 70, 229, 0.2);
  --focus-ring: rgba(79, 70, 229, 0.3);
  
  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-blur: blur(20px);
  
  /* Shadows */
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 8px 25px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 25px 50px rgba(0, 0, 0, 0.4);
  --shadow-glow: 0 0 30px rgba(79, 70, 229, 0.3);
  
  /* Starfield Pattern */
  --starfield-pattern: 
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  --starfield-size: 150px 100px;
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.6s ease;
}

/* Light Theme Overrides */
:root[data-theme="light"], 
.light-theme {
  --primary-bg: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  --secondary-bg: rgba(0, 0, 0, 0.03);
  --card-bg: rgba(255, 255, 255, 0.8);
  --border-color: rgba(0, 0, 0, 0.1);
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: rgba(0, 0, 0, 0.6);
  
  /* Interactive States for Light Mode */
  --hover-bg: rgba(0, 0, 0, 0.05);
  --active-bg: rgba(79, 70, 229, 0.1);
  --focus-ring: rgba(79, 70, 229, 0.2);
  
  /* Glassmorphism for Light Mode */
  --glass-bg: rgba(255, 255, 255, 0.7);
  --glass-border: rgba(0, 0, 0, 0.1);
  --glass-blur: blur(15px);
  
  /* Shadows for Light Mode */
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 8px 25px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 25px 50px rgba(0, 0, 0, 0.15);
  --shadow-glow: 0 0 30px rgba(79, 70, 229, 0.2);
  
  /* Starfield Pattern for Light Mode */
  --starfield-pattern: 
    radial-gradient(1px 1px at 20px 30px, rgba(79, 70, 229, 0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(124, 58, 237, 0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(236, 72, 153, 0.06), transparent);
}

/* Theme Transition Animation */
* {
  transition: 
    background-color var(--transition-normal),
    border-color var(--transition-normal),
    color var(--transition-normal),
    box-shadow var(--transition-normal);
}

/* Disable transitions during theme change to prevent flashing */
.theme-transitioning * {
  transition: none !important;
}

/* Smooth scroll behavior for navigation */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
